/* eslint-disable i18next/no-literal-string */
import { FC, useCallback, useMemo, useRef, useState } from 'react'
import styles from './phishing-campaigns.module.scss'
import classNamesBind from 'classnames/bind'
import { PhishingCampaignsProps } from './phishing-campaigns.d'
import {
  Button,
  Loader,
  Pagination,
  RoundedButton,
  Switch,
  TabItem,
  TabsNew,
  PageTitle,
  SearchInput,
} from '@/shared/ui'
import { usePhishingCampaigns } from './use-phishing-campaigns'
import { PhishingCampaignCard } from '@/shared/components'
import { useTranslation } from 'react-i18next'
import { ConfirmModal } from '../../../shared/modals/confirm-modal'
import { PhishingCampaignsFilters } from './phishing-campaigns-filters'
import { LoadingDataStatus } from '@/shared/components/loading-data-status'
import { SortBy, SortOrder } from '@/entities/phishing'
import { useAppDispatch, useAppSelector } from '@/store'
import { phishingCampaignsActions, phishingCampaignsSelectors } from './slice'
import { useEvent } from '@/shared/hooks'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { ChevroneSmallIcon } from '@/shared/ui/Icon/icons/components'
import { IconSortDirection } from '@/shared/components/icon-sort-direction'
import { mapFormToApiParams } from './helpers'
import { format } from '@/shared/helpers/date'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'pages__phishing-campaigns'

export const PhishingCampaignsList: FC<PhishingCampaignsProps.ListProps> = props => {
  const { className, list, onCardClick, header } = props
  const activeSort = useAppSelector(phishingCampaignsSelectors.selectSorting)
  const dispatch = useAppDispatch()
  return (
    <div className={cx('list-wrapper', className)}>
      {header}
      <div className={cx('header')}>
        <PhishingCampaignsListSort
          activeSort={activeSort.sort_by}
          onChange={s => {
            dispatch(phishingCampaignsActions.setFilters({ sort_by: s }))
          }}
          activeSortDirection={activeSort.sort_order}
        />
      </div>
      <div className={cx('items')}>
        {list.map(item => (
          <PhishingCampaignCard
            key={`campaign-card-${item.id}`}
            className={cx('list__item')}
            data={item}
            onClick={onCardClick}
          />
        ))}
      </div>
    </div>
  )
}

type PhishingCampaignsListSortProps = {
  activeSort?: SortBy
  activeSortDirection?: SortOrder
  onChange: (s: SortBy) => void
}

export const PhishingCampaignsListSort: FC<PhishingCampaignsListSortProps> = props => {
  const { t } = useTranslation(TRANSLATION_FILE)

  const SORT_LABELS: Record<SortBy, string> = useMemo(() => {
    return {
      START_DATE: t('start_date'),
      END_DATE: t('end_date'),
      LINK_LIFETIME: t('link_lifetime'),
      TARGETS: t('targets'),
      INDICATOR: t('indicator'),
    }
  }, [t])
  const dispatch = useAppDispatch()
  const wrapper = useRef<HTMLDivElement>(null)
  const [isActive, setIsActive] = useState(false)
  const activeSort = props.activeSort
  const handleChange = (s: SortBy) => {
    props.onChange(s)
    setIsActive(false)
  }

  const handleClick = (isActive: boolean) => {
    setIsActive(!isActive)
  }

  const handleOutsideClick = useCallback((e: Event) => {
    if (!wrapper.current) return

    if (e.composedPath().indexOf(wrapper.current) === -1) {
      handleClick(true)
    }
  }, [])

  useEvent('click', handleOutsideClick, window)

  return (
    <div className={cx('wrapper__sort')} ref={wrapper} onClick={() => handleClick(isActive)}>
      <div className={cx('text')}>
        {activeSort ? SORT_LABELS[activeSort] : t('select_sort')}
        <IconWrapper color='gray80' className={cx('icon')} direction={!isActive ? 'right' : 'left'}>
          <ChevroneSmallIcon />
        </IconWrapper>
        <IconSortDirection
          startDirection={'asc'}
          direction={props.activeSortDirection}
          onChange={(dir, e) => {
            dispatch(phishingCampaignsActions.setFilters({ sort_order: dir }))
            e?.stopPropagation()
          }}
        />
      </div>
      {isActive && (
        <div className={cx('listWrapper')} style={styles} onClick={e => e.stopPropagation()}>
          <div className={cx('listInner')}>
            {Object.keys(SORT_LABELS).map(s => {
              const sort = s as SortBy
              const isActive = sort === activeSort

              return (
                <span
                  key={`list-item-${s}`}
                  className={cx('listItem', { active: isActive })}
                  onClick={() => handleChange(sort)}
                >
                  {SORT_LABELS[sort]}
                </span>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}

export const PhishingCampaigns: FC<PhishingCampaignsProps.Props> = props => {
  const { className } = props
  const { t } = useTranslation(TRANSLATION_FILE)

  const {
    isLoading,
    isFetching,
    onClickAutophish,
    autophishEnabled,
    onAddClick,
    isUpdateAutophishLoading,
    phishingCampaignsCount,
    tabs,
    tab,
    data,
    ACTUAL_PAGE,
    PAGE_ITEMS_LIMIT,
    isPaginationVisible,
    setPage: onSetPage,
    onCardClick,
    hasError,
    autoPhishConfirmOpen,
    setAutoPhishConfirmOpen,
    setFilters,
    filters,
    campaignStatus,
    hasFilter,
    isModalOpen,
  } = usePhishingCampaigns()
  const dispatch = useAppDispatch()

  return (
    <div className={cx('wrapper', className)}>
      <div className={cx('title-wrapper')}>
        <div className={cx('title-wrapper__block')}>
          <PageTitle className={cx('title')}>{t('commons:campaigns')}</PageTitle>
          <SearchInput
            className={cx('search')}
            classNameWrapper={cx('searchWrapper')}
            placeholder={t('search')}
            onChange={search => setFilters({ search })}
            value={filters.search ?? ''}
          />
        </div>
        <div className={cx('autophish-wrapper')}>
          {!isLoading && (
            <div className={cx('autophish')}>
              <span className={cx('autophish-title')}>{t('auto_campaigns')}</span>
              <Switch
                onChange={() => {
                  if (!autophishEnabled) {
                    setAutoPhishConfirmOpen(true)
                    return
                  }

                  onClickAutophish()
                }}
                customValue={autophishEnabled}
                className={cx('autophish-switch')}
                disabled={isUpdateAutophishLoading}
              />
            </div>
          )}

          <RoundedButton onClick={onAddClick} size='40' />
        </div>
      </div>
      <ConfirmModal
        open={autoPhishConfirmOpen}
        setOpen={setAutoPhishConfirmOpen}
        title={t('auto_confirm_title')}
        wrapperClassname={cx('confirm-modal__wrapper')}
        description={t('auto_confirm_description')}
        onClose={onClickAutophish}
        closeText={t('auto_confirm_close')}
        confirmText={t('auto_confirm_confirm')}
      />
      {isLoading && (
        <div className={cx('loader-wrapper')}>
          <Loader size='56' className='loader_centered' />
        </div>
      )}
      {!isLoading && hasError && (
        <div className={cx('error-text')}>
          {t('commons:error_occurred_while_receiving_data')} :(
        </div>
      )}
      {!isLoading && !hasError && !phishingCampaignsCount && (
        <div className={cx('empty-wrapper')}>
          <span>{t('commons:dont_have_newsletters')}</span>
          <Button onClick={onAddClick}>{t('commons:new_newsletter')}</Button>
        </div>
      )}
      {!isLoading && !hasError && !!phishingCampaignsCount && (
        <>
          <div className={cx('tabs-wrapper')}>
            {tab && tabs && (
              <TabsNew
                activeTab={tab}
                tabClassname={cx('tabs__item')}
                activeTabClassname={cx('tabs__item_active')}
                tabsClassname={cx('tabs')}
                className={cx('tabs__container')}
                onChange={v => setFilters({ status: v })}
              >
                {tabs.map(tab => {
                  return (
                    <TabItem
                      key={`tab-${tab.value}`}
                      label={tab.label}
                      value={tab.value}
                      count={tab.count}
                    >
                      <></>
                    </TabItem>
                  )
                })}
              </TabsNew>
            )}
            <PhishingCampaignsFilters
              onModalOpen={v => dispatch(phishingCampaignsActions.setFilterModalOpen(v))}
              resetFilters={() => dispatch(phishingCampaignsActions.resetFilters())}
              isModalOpen={isModalOpen}
              filters={filters}
              onSubmit={data => {
                dispatch(
                  phishingCampaignsActions.setFilters(
                    mapFormToApiParams(data, date => format(date, 'yyyy-MM-dd')),
                  ),
                )
                dispatch(phishingCampaignsActions.setFilterModalOpen(false))
                dispatch(phishingCampaignsActions.setPage(0))
              }}
              hasFilter={!!hasFilter}
            />
          </div>

          {!!data?.data?.length && (
            <PhishingCampaignsList list={data.data} onCardClick={onCardClick} />
          )}

          {!data?.data?.length && (
            <LoadingDataStatus
              data={data?.data}
              fetchStatus={campaignStatus}
              dataLength={data?.data?.length}
              createHandler={onAddClick}
              texts={{ empty: t('empty') }}
              wrapperClass={cx('data-loading__wrapper')}
            />
          )}

          {isPaginationVisible && (
            <Pagination
              currentPage={ACTUAL_PAGE}
              limit={PAGE_ITEMS_LIMIT}
              onChange={onSetPage}
              total={data?.total_count || 0}
              isLoading={isLoading || isFetching}
            />
          )}
        </>
      )}
    </div>
  )
}

export default PhishingCampaigns
