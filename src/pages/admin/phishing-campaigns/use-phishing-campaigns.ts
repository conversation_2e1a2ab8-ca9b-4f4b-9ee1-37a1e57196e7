import { useEffect, useMemo } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'

import { phishingCampaignsActions, phishingCampaignsSelectors } from './slice'
import { type IPhishingStatus, phishingMutations, phishingQueries } from '@/entities/phishing'
import { useDebounceValue } from 'usehooks-ts'
import { useAppSelector } from '@/store'

export const usePhishingCampaigns = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { t } = useTranslation()

  const { filters, limit, offset, autoPhishConfirmOpen } = useSelector(
    phishingCampaignsSelectors.selectPhishingCampaigns,
  )
  const [debouncedSearch] = useDebounceValue(filters.search, 500)

  const {
    data: autophishData,
    isLoading: isAutophishLoading,
    isFetching: isAutophishFetching,
    isError: isAutophishError,
  } = phishingQueries.useGetAutophishQuery()

  const {
    data: phishingStatuses,
    isLoading: isStatusesLoading,
    isError: isStatusesError,
  } = phishingQueries.useGetPhishingCampaignsStatusesQuery({ params: { by_tag: false } })

  const {
    data,
    isLoading,
    isError,
    status: campaignStatus,
  } = phishingQueries.useGetPhishingCampaignsQuery(
    {
      filters: {
        ...filters,
        search: debouncedSearch,
      },
      limit,
      offset,
    },
    {
      skip: isStatusesLoading || !filters.status,
    },
  )

  const [updateAutophish, { isLoading: isUpdateAutophishLoading }] =
    phishingMutations.useUpdateAutophishMutation()

  const autophishEnabled = autophishData?.enabled || false

  const onClickAutophish = () => {
    updateAutophish({ enabled: !autophishEnabled }).unwrap()
  }

  const onAddClick = () => {
    navigate('/lk/admin/phishing/campaigns/create')
  }

  const onCardClick = (id: UUID) => {
    if (id) navigate('/lk/admin/phishing/campaigns/' + id)
  }

  const TABS_CONFIG = useMemo(
    () => [
      { label: t('commons:active'), value: 'active', count: 0 },
      { label: t('commons:postponed'), value: 'planned', count: 0 },
      { label: t('commons:completed'), value: 'completed', count: 0 },
    ],
    [t],
  )

  const filtredTabs = useMemo(
    () =>
      phishingStatuses
        ? TABS_CONFIG.filter(t => !!phishingStatuses[t.value as IPhishingStatus]).map(t => {
            t.count = phishingStatuses[t.value as IPhishingStatus]
            return t
          })
        : [],
    [phishingStatuses, TABS_CONFIG],
  )

  // --- если таб не выбран, то выставляем первый доступный
  useEffect(() => {
    if (!filtredTabs || !filtredTabs.length) return
    if (filtredTabs.some(filteredTab => filteredTab?.value === filters?.status)) return
    const tab = filtredTabs?.[0].value

    dispatch(phishingCampaignsActions.setFilters({ status: tab as IPhishingStatus }))
  }, [filtredTabs, filters.status, dispatch])

  // --- derived data
  const isDataLoading = isAutophishLoading || isStatusesLoading || isLoading
  const phishingCampaignsCount =
    phishingStatuses &&
    !isDataLoading &&
    phishingStatuses?.active + phishingStatuses?.completed + phishingStatuses?.planned

  const hasError = isError && isStatusesError && isAutophishError

  const COUNT_PAGE = Math.ceil((data?.total_count || 0) / limit)
  const isPaginationVisible = data?.data && COUNT_PAGE > 1
  const hasFilter = useAppSelector(phishingCampaignsSelectors.selectHasFilters)
  const isModalOpen = useAppSelector(phishingCampaignsSelectors.selectFilterModalOpen)
  return {
    isLoading: isDataLoading,
    isFetching: isAutophishFetching,
    isUpdateAutophishLoading,
    hasError,
    data,
    tabs: filtredTabs,
    tab: filters.status,
    ACTUAL_PAGE: offset / limit,
    PAGE_ITEMS_LIMIT: limit,
    phishingCampaignsCount,
    autophishEnabled,
    onClickAutophish,
    onAddClick,
    onCardClick,
    setPage: (p: number) => dispatch(phishingCampaignsActions.setPage(p)),
    setFilters: (f: Partial<typeof filters>) => dispatch(phishingCampaignsActions.setFilters(f)),
    resetFilters: () => dispatch(phishingCampaignsActions.resetFilters()),
    setAutoPhishConfirmOpen: (v: boolean) =>
      dispatch(phishingCampaignsActions.setAutoPhishConfirmOpen(v)),
    autoPhishConfirmOpen,
    isPaginationVisible,
    filters,
    campaignStatus,
    hasFilter,
    isModalOpen,
  }
}
